"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { FormData } from "../page";

interface GoodMoralFormProps {
  onSubmit: (data: FormData) => void;
  initialData: FormData;
}

export function GoodMoralForm({ onSubmit, initialData }: GoodMoralFormProps) {
  const [formData, setFormData] = useState<FormData>(initialData);
  const [imagePreview, setImagePreview] = useState<string>("");

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setImagePreview(result);
        handleInputChange("image", result);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Basic validation
    const requiredFields = [
      "firstName",
      "middleName",
      "lastName",
      "age",
      "province",
      "municipality",
      "postalAddress",
      "day",
      "month",
      "year",
      "mayor",
      "ctcNo",
      "orNo",
    ];

    const missingFields = requiredFields.filter(
      (field) => !formData[field as keyof FormData]
    );

    if (missingFields.length > 0) {
      alert(`Please fill in all required fields: ${missingFields.join(", ")}`);
      return;
    }

    onSubmit(formData);
  };

  return (
    <div className="max-w-4xl mx-auto">
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl font-bold text-center">
            Good Moral Certificate Application
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Personal Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-primary">
                Personal Information
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="firstName">First Name</Label>
                  <Input
                    id="firstName"
                    value={formData.firstName}
                    onChange={(e) =>
                      handleInputChange("firstName", e.target.value)
                    }
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="middleName">Middle Name</Label>
                  <Input
                    id="middleName"
                    value={formData.middleName}
                    onChange={(e) =>
                      handleInputChange("middleName", e.target.value)
                    }
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="lastName">Last Name</Label>
                  <Input
                    id="lastName"
                    value={formData.lastName}
                    onChange={(e) =>
                      handleInputChange("lastName", e.target.value)
                    }
                    required
                  />
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="age">Age</Label>
                  <Input
                    id="age"
                    type="number"
                    value={formData.age}
                    onChange={(e) => handleInputChange("age", e.target.value)}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="postalAddress">Barangay Address</Label>
                  <Input
                    id="postalAddress"
                    value={formData.postalAddress}
                    onChange={(e) =>
                      handleInputChange("postalAddress", e.target.value)
                    }
                    placeholder="e.g., Brgy. San Roque"
                    required
                  />
                </div>
              </div>
            </div>

            {/* Location Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-primary">
                Location Information
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="province">Province</Label>
                  <Input
                    id="province"
                    value={formData.province}
                    onChange={(e) =>
                      handleInputChange("province", e.target.value)
                    }
                    placeholder="e.g., Leyte"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="municipality">Municipality</Label>
                  <Input
                    id="municipality"
                    value={formData.municipality}
                    onChange={(e) =>
                      handleInputChange("municipality", e.target.value)
                    }
                    placeholder="e.g., Tanauan"
                    required
                  />
                </div>
              </div>
            </div>

            {/* Document Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-primary">
                Document Information
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="day">Day</Label>
                  <Input
                    id="day"
                    type="number"
                    min="1"
                    max="31"
                    value={formData.day}
                    onChange={(e) => handleInputChange("day", e.target.value)}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="month">Month</Label>
                  <Input
                    id="month"
                    value={formData.month}
                    onChange={(e) => handleInputChange("month", e.target.value)}
                    placeholder="e.g., January"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="year">Year</Label>
                  <Input
                    id="year"
                    type="number"
                    value={formData.year}
                    onChange={(e) => handleInputChange("year", e.target.value)}
                    required
                  />
                </div>
              </div>
            </div>

            {/* Official Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-primary">
                Official Information
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="mayor">Mayor Name</Label>
                  <Input
                    id="mayor"
                    value={formData.mayor}
                    onChange={(e) => handleInputChange("mayor", e.target.value)}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="ctcNo">CTC No.</Label>
                  <Input
                    id="ctcNo"
                    value={formData.ctcNo}
                    onChange={(e) => handleInputChange("ctcNo", e.target.value)}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="orNo">O.R. No.</Label>
                  <Input
                    id="orNo"
                    value={formData.orNo}
                    onChange={(e) => handleInputChange("orNo", e.target.value)}
                    required
                  />
                </div>
              </div>
            </div>

            {/* Image Upload */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-primary">
                Applicant's Photo
              </h3>
              <div className="space-y-2">
                <Label htmlFor="image">Upload Applicant's Photo</Label>
                <Input
                  id="image"
                  type="file"
                  accept="image/*"
                  onChange={handleImageChange}
                  className="cursor-pointer"
                />
                {imagePreview && (
                  <div className="mt-2">
                    <img
                      src={imagePreview}
                      alt="Mayor preview"
                      className="w-32 h-32 object-cover border rounded-md cursor-pointer"
                      onClick={() => document.getElementById("image")?.click()}
                    />
                  </div>
                )}
              </div>
            </div>

            <div className="flex justify-center pt-6">
              <Button type="submit" size="lg" className="px-8">
                Generate Certificate
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
